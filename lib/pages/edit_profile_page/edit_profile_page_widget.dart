import 'dart:convert';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:dio/dio.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_theme.dart';
import '/pages/app_button/app_button_widget.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../Classified_App/classified_app_model.dart';
import '../../Classified_App/form_field_controller.dart';
import '../../Classified_App/internationalization.dart';
import '../../ApiUtils.dart';
import '../../store/AppStore.dart';
// import '../../widget/toasty.dart'; // Not available
import 'edit_profile_page_model.dart';
export 'edit_profile_page_model.dart';

// Global app store instance
final appStore = AppStore();

class EditProfilePageWidget extends StatefulWidget {
  const EditProfilePageWidget({super.key});

  @override
  State<EditProfilePageWidget> createState() => _EditProfilePageWidgetState();
}

class _EditProfilePageWidgetState extends State<EditProfilePageWidget> {
  late EditProfilePageModel _model;
  String? profile_image;
  XFile? uploadProfile;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final ImagePicker _picker = ImagePicker();

  // Helper method to show messages
  void showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
      ),
    );
  }

  productImage() async {
    _picker
        .pickImage(
      source: ImageSource.gallery,
    )
        .then((XFile? recordedVideo) {
      if (recordedVideo != null && recordedVideo.path != null) {
        setState(() {
          uploadProfile = recordedVideo;
        });

        _cropImage();
      }
    });
  }

  Future _cropImage() async {
    if (uploadProfile != null) {
      CroppedFile? cropped = await ImageCropper().cropImage(
          sourcePath: uploadProfile!.path,
          aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1),
          uiSettings: [
            AndroidUiSettings(
                toolbarTitle: 'Crop',
                cropGridColor: Colors.black,
                initAspectRatio: CropAspectRatioPreset.original,
                lockAspectRatio: false),
            IOSUiSettings(title: 'Crop')
          ]);

      if (cropped != null) {
        sendVideoDio(cropped.path, cropped.path.split('/').last);
      }
    }
  }

  sendVideoDio(String pathname, String fileName) async {
    try {
      FormData formData = FormData.fromMap({
        "profile_image": await MultipartFile.fromFile(pathname,
            filename: uploadProfile!.name),
        "customer_id": appStore.user_id,
      });
      Response response = await Dio()
          .post("${ApiUtils.BASE_URL}update-profile-image", data: formData);
      if (response.statusCode == 200) {
        showMessage("Successfully Profile Pic Update!");
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('profile_image', response.data['profile_image']);
        setState(() {
          profile_image = response.data['profile_image'];
          appStore.profile_image = response.data['profile_image'];
        });
      } else {
        showMessage(response.data['message'], isError: true);
      }
    } catch (e) {
      showMessage("$e", isError: true);
    }
  }

  void updateProfile(context) {
    bool emailValid = RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(_model.textController3?.text ?? '');
    if (_model.textController1?.text == '' ||
        _model.textController1?.text == null) {
      showMessage('Enter First Name', isError: true);
    } else if (_model.textController2?.text == '' ||
        _model.textController2?.text == null) {
      showMessage('Enter Last Name', isError: true);
    } else if (_model.textController4?.text == '' ||
        _model.textController4?.text == null) {
      showMessage('Enter Valid Mobile Number', isError: true);
    } else if (emailValid == false) {
      showMessage('Enter Valid Email Address', isError: true);
    } else {
      try {
        showLoadingDialog(context);
        http
            .post(
          Uri.parse("${ApiUtils.BASE_URL}update-profile"),
          headers: <String, String>{
            'Content-Type': 'application/json; charset=UTF-8',
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          },
          body: jsonEncode(<String, dynamic>{
            'customer_id': appStore.user_id,
            'first_name': _model.textController1?.text ?? '',
            'last_name': _model.textController2?.text ?? '',
            'email': _model.textController3?.text ?? '',
            'mobile': _model.textController4?.text ?? '',
            'instagram': _model.textController5?.text ?? '',
            'x_com': _model.textController6?.text ?? '',
            'facebook': _model.textController7?.text ?? '',
          }),
        )
            .then((response) {
          Navigator.of(context, rootNavigator: true).pop(false);
          Map mapValue = json.decode(response.body);
          if (response.statusCode == 200) {
            String name = (_model.textController1?.text ?? '') +
                " " +
                (_model.textController2?.text ?? '');
            appStore.updateProfile(name, _model.textController3?.text ?? '',
                profile_image, _model.textController4?.text ?? '', context,
                instagram: _model.textController5?.text ?? '',
                xCom: _model.textController6?.text ?? '',
                facebook: _model.textController7?.text ?? '');

            showMessage(mapValue['message']);
          } else {
            showMessage(mapValue['message'], isError: true);
          }
        });
      } catch (e) {
        Navigator.of(context, rootNavigator: true).pop(false);
        showMessage("$e", isError: true);
      }
    }
  }

  void getProfile() {
    http.get(Uri.parse("${ApiUtils.BASE_URL}get-profile/${appStore.user_id}"),
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
          'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
        }).then((response) {
      if (response.statusCode == 200) {
        Map mapValue = json.decode(response.body);
        setState(() {
          profile_image = mapValue['profile_image'] ?? null;
          _model.textController1?.text = mapValue['first_name'] ?? '';
          _model.textController2?.text = mapValue['last_name'] ?? '';
          _model.textController3?.text = mapValue['email'] ?? '';
          _model.textController4?.text = mapValue['mobile'] ?? '';
          _model.textController5?.text = mapValue['instagram'] ?? '';
          _model.textController6?.text = mapValue['x_com'] ?? '';
          _model.textController7?.text = mapValue['facebook'] ?? '';
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => EditProfilePageModel());

    _model.textController1 ??= TextEditingController();
    _model.textFieldFocusNode1 ??= FocusNode();

    _model.textController2 ??= TextEditingController();
    _model.textFieldFocusNode2 ??= FocusNode();

    _model.textController3 ??= TextEditingController();
    _model.textFieldFocusNode3 ??= FocusNode();
    _model.textController4 ??= TextEditingController();
    _model.textFieldFocusNode4 ??= FocusNode();

    _model.textController5 ??= TextEditingController();
    _model.textFieldFocusNode5 ??= FocusNode();

    _model.textController6 ??= TextEditingController();
    _model.textFieldFocusNode6 ??= FocusNode();

    _model.textController7 ??= TextEditingController();
    _model.textFieldFocusNode7 ??= FocusNode();

    getProfile();
  }

  // Helper method to build elegant text fields
  Widget _buildElegantTextField({
    required String label,
    required TextEditingController? controller,
    required FocusNode? focusNode,
    required String hintText,
    required IconData icon,
    TextInputAction textInputAction = TextInputAction.next,
    TextInputType keyboardType = TextInputType.text,
    String? prefixText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Satoshi',
            fontSize: 14.0,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        SizedBox(height: 8.0),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.02),
                blurRadius: 8.0,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            focusNode: focusNode,
            textInputAction: textInputAction,
            keyboardType: keyboardType,
            style: TextStyle(
              fontFamily: 'Satoshi',
              fontSize: 16.0,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: TextStyle(
                fontFamily: 'Satoshi',
                fontSize: 16.0,
                fontWeight: FontWeight.w400,
                color: Colors.grey[400],
              ),
              prefixIcon: Container(
                margin: EdgeInsets.all(12.0),
                padding: EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: ClassifiedAppTheme.of(context)
                      .primary
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: Icon(
                  icon,
                  color: ClassifiedAppTheme.of(context).primary,
                  size: 20.0,
                ),
              ),
              prefixText: prefixText,
              prefixStyle: TextStyle(
                fontFamily: 'Satoshi',
                fontSize: 16.0,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
              filled: true,
              fillColor: Colors.grey[50],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1.0,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide(
                  color: ClassifiedAppTheme.of(context).primary,
                  width: 2.0,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide(
                  color: Colors.red[400]!,
                  width: 1.0,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide(
                  color: Colors.red[400]!,
                  width: 2.0,
                ),
              ),
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
            ),
          ),
        ),
        SizedBox(height: 20.0),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: Colors.grey[50],
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              // Custom elegant app bar
              Container(
                width: double.infinity,
                height: 80.0,
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10.0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          width: 40.0,
                          height: 40.0,
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            color: Colors.grey[700],
                            size: 20.0,
                          ),
                        ),
                      ),
                      Text(
                        'Edit Profile',
                        style: TextStyle(
                          fontFamily: 'Satoshi',
                          fontSize: 20.0,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                        ),
                      ),
                      Container(width: 40.0), // Spacer for centering
                    ],
                  ),
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Profile Header Section
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(30.0),
                            bottomRight: Radius.circular(30.0),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 15.0,
                              offset: Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              24.0, 32.0, 24.0, 32.0),
                          child: Column(
                            children: [
                              // Profile Image with elegant design
                              Stack(
                                alignment: AlignmentDirectional(1.0, 1.0),
                                children: [
                                  Container(
                                    width: 120.0,
                                    height: 120.0,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: LinearGradient(
                                        colors: [
                                          ClassifiedAppTheme.of(context)
                                              .primary,
                                          ClassifiedAppTheme.of(context)
                                              .secondary,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: ClassifiedAppTheme.of(context)
                                              .primary
                                              .withValues(alpha: 0.3),
                                          blurRadius: 20.0,
                                          offset: Offset(0, 8),
                                        ),
                                      ],
                                    ),
                                    child: Padding(
                                      padding: EdgeInsets.all(4.0),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.white,
                                        ),
                                        child: Padding(
                                          padding: EdgeInsets.all(4.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(60.0),
                                            child: profile_image == null
                                                ? Container(
                                                    width: 108.0,
                                                    height: 108.0,
                                                    decoration: BoxDecoration(
                                                      color: Colors.grey[100],
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: Icon(
                                                      Icons.person,
                                                      size: 50.0,
                                                      color: Colors.grey[400],
                                                    ),
                                                  )
                                                : CachedNetworkImage(
                                                    width: 108.0,
                                                    height: 108.0,
                                                    fit: BoxFit.cover,
                                                    imageUrl:
                                                        "${ApiUtils.profile_files}${profile_image}",
                                                    placeholder:
                                                        (context, url) =>
                                                            Container(
                                                      width: 108.0,
                                                      height: 108.0,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey[100],
                                                        shape: BoxShape.circle,
                                                      ),
                                                      child: Center(
                                                        child:
                                                            CircularProgressIndicator(
                                                          strokeWidth: 2.0,
                                                          valueColor:
                                                              AlwaysStoppedAnimation<
                                                                  Color>(
                                                            ClassifiedAppTheme
                                                                    .of(context)
                                                                .primary,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    errorWidget:
                                                        (context, url, error) =>
                                                            Container(
                                                      width: 108.0,
                                                      height: 108.0,
                                                      decoration: BoxDecoration(
                                                        color: Colors.grey[100],
                                                        shape: BoxShape.circle,
                                                      ),
                                                      child: Icon(
                                                        Icons.person,
                                                        size: 50.0,
                                                        color: Colors.grey[400],
                                                      ),
                                                    ),
                                                  ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Camera button with elegant design
                                  GestureDetector(
                                    onTap: () => productImage(),
                                    child: Container(
                                      width: 40.0,
                                      height: 40.0,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context)
                                            .primary,
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                ClassifiedAppTheme.of(context)
                                                    .primary
                                                    .withValues(alpha: 0.4),
                                            blurRadius: 12.0,
                                            offset: Offset(0, 4),
                                          ),
                                        ],
                                      ),
                                      child: Icon(
                                        Icons.camera_alt,
                                        color: Colors.white,
                                        size: 20.0,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 16.0),
                              Text(
                                'Update Profile Picture',
                                style: TextStyle(
                                  fontFamily: 'Satoshi',
                                  fontSize: 16.0,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 24.0),

                      // Form Fields Container
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20.0),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 15.0,
                              offset: Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              24.0, 32.0, 24.0, 32.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Personal Information',
                                style: TextStyle(
                                  fontFamily: 'Satoshi',
                                  fontSize: 20.0,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey[800],
                                ),
                              ),
                              SizedBox(height: 24.0),

                              // First Name Field
                              _buildElegantTextField(
                                label: 'First Name',
                                controller: _model.textController1,
                                focusNode: _model.textFieldFocusNode1,
                                hintText: 'Enter your first name',
                                icon: Icons.person_outline,
                                textInputAction: TextInputAction.next,
                              ),

                              // Last Name Field
                              _buildElegantTextField(
                                label: 'Last Name',
                                controller: _model.textController2,
                                focusNode: _model.textFieldFocusNode2,
                                hintText: 'Enter your last name',
                                icon: Icons.person_outline,
                                textInputAction: TextInputAction.next,
                              ),

                              // Email Field
                              _buildElegantTextField(
                                label: 'Email Address',
                                controller: _model.textController3,
                                focusNode: _model.textFieldFocusNode3,
                                hintText: 'Enter your email address',
                                icon: Icons.email_outlined,
                                keyboardType: TextInputType.emailAddress,
                                textInputAction: TextInputAction.next,
                              ),

                              // Mobile Field
                              _buildElegantTextField(
                                label: 'Mobile Number',
                                controller: _model.textController4,
                                focusNode: _model.textFieldFocusNode4,
                                hintText: 'Enter your mobile number',
                                icon: Icons.phone_outlined,
                                keyboardType: TextInputType.phone,
                                prefixText: '+971 ',
                                textInputAction: TextInputAction.next,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 24.0),

                      // Social Media Section
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20.0),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 15.0,
                              offset: Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              24.0, 32.0, 24.0, 32.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(8.0),
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .primary
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(10.0),
                                    ),
                                    child: Icon(
                                      Icons.share,
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      size: 20.0,
                                    ),
                                  ),
                                  SizedBox(width: 12.0),
                                  Text(
                                    'Social Media Links',
                                    style: TextStyle(
                                      fontFamily: 'Satoshi',
                                      fontSize: 20.0,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey[800],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 8.0),
                              Text(
                                'Connect your social media profiles (optional)',
                                style: TextStyle(
                                  fontFamily: 'Satoshi',
                                  fontSize: 14.0,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.grey[500],
                                ),
                              ),
                              SizedBox(height: 24.0),

                              // Instagram Field
                              _buildElegantTextField(
                                label: 'Instagram',
                                controller: _model.textController5,
                                focusNode: _model.textFieldFocusNode5,
                                hintText: 'Enter Instagram username or URL',
                                icon: Icons.camera_alt,
                                keyboardType: TextInputType.url,
                                textInputAction: TextInputAction.next,
                              ),

                              // X.com Field
                              _buildElegantTextField(
                                label: 'X.com (Twitter)',
                                controller: _model.textController6,
                                focusNode: _model.textFieldFocusNode6,
                                hintText: 'Enter X.com username or URL',
                                icon: Icons.alternate_email,
                                keyboardType: TextInputType.url,
                                textInputAction: TextInputAction.next,
                              ),

                              // Facebook Field
                              _buildElegantTextField(
                                label: 'Facebook',
                                controller: _model.textController7,
                                focusNode: _model.textFieldFocusNode7,
                                hintText: 'Enter Facebook username or URL',
                                icon: Icons.facebook,
                                keyboardType: TextInputType.url,
                                textInputAction: TextInputAction.done,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 32.0),

                      // Save Button Section
                      Container(
                        width: double.infinity,
                        padding: EdgeInsetsDirectional.fromSTEB(
                            24.0, 0.0, 24.0, 32.0),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                ClassifiedAppTheme.of(context).primary,
                                ClassifiedAppTheme.of(context).secondary,
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius: BorderRadius.circular(16.0),
                            boxShadow: [
                              BoxShadow(
                                color: ClassifiedAppTheme.of(context)
                                    .primary
                                    .withValues(alpha: 0.3),
                                blurRadius: 20.0,
                                offset: Offset(0, 8),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(16.0),
                              onTap: () => updateProfile(context),
                              child: Container(
                                width: double.infinity,
                                height: 56.0,
                                child: Center(
                                  child: Text(
                                    'Save Changes',
                                    style: TextStyle(
                                      fontFamily: 'Satoshi',
                                      fontSize: 18.0,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }
}
